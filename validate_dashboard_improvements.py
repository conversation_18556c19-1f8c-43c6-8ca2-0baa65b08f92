#!/usr/bin/env python3
"""
Validation script for inventory dashboard improvements
Tests the new Cash Flow Efficiency Meter and consolidated cards
"""

import sys
import os
import pandas as pd
import json

# Add the project root to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'digitoryjobsv4'))

def create_sample_data():
    """Create sample store variance data for testing"""
    sample_data = [
        {
            'Location': 'Main Store',
            'Category': 'Vegetables',
            'SubCategory': 'Fresh Vegetables',
            'Item Code': 'VEG001',
            'Item Name': 'Tomatoes',
            'EntryType': 'Inventory',
            'PackageName': 'Kg',
            'UOM': 'Kg',
            'TaxPercentage': 5.0,
            'WACwithTax': 50.0,
            'OpeningQty': 100,
            'PurchaseQty': 200.0,
            'IndentQty': 150.0,
            'IbtInQty': 0,
            'IbtOutQty': 0,
            'SpoilageQty': 5.0,
            'ClosingQty': 145,
            'PhysicalClosingQty': 145,
            'Variance': 0,
            'VarianceAmount': 0.0,
            'OpeningAmount': 5000.0,
            'PurchaseAmount': 10000.0,
            'IndentAmount': 7500.0,
            'IbtInAmount': 0.0,
            'IbtOutAmount': 0.0,
            'SpoilageAmount': 250.0,
            'ClosingAmount': 7250.0,
            'PhysicalClosingAmount': 7250.0,
            'Return To Store In Qty': 10.0,
            'Return To Store In Amount': 500.0,
            'Return-Qty Amount': 100.0
        },
        {
            'Location': 'Main Store',
            'Category': 'Dairy',
            'SubCategory': 'Milk Products',
            'Item Code': 'DAI001',
            'Item Name': 'Fresh Milk',
            'EntryType': 'Inventory',
            'PackageName': 'Liter',
            'UOM': 'Liter',
            'TaxPercentage': 0.0,
            'WACwithTax': 60.0,
            'OpeningQty': 50,
            'PurchaseQty': 100.0,
            'IndentQty': 120.0,
            'IbtInQty': 0,
            'IbtOutQty': 0,
            'SpoilageQty': 2.0,
            'ClosingQty': 28,
            'PhysicalClosingQty': 28,
            'Variance': 0,
            'VarianceAmount': 0.0,
            'OpeningAmount': 3000.0,
            'PurchaseAmount': 6000.0,
            'IndentAmount': 7200.0,
            'IbtInAmount': 0.0,
            'IbtOutAmount': 0.0,
            'SpoilageAmount': 120.0,
            'ClosingAmount': 1680.0,
            'PhysicalClosingAmount': 1680.0,
            'Return To Store In Qty': 0.0,
            'Return To Store In Amount': 0.0,
            'Return-Qty Amount': 0.0
        }
    ]
    return pd.DataFrame(sample_data)

def test_dashboard_improvements():
    """Test the improved dashboard functionality"""
    try:
        from app.utility.dashboard_agents import generate_inventory_dashboard
        
        print("✅ Successfully imported generate_inventory_dashboard")
        
        # Create sample data
        df = create_sample_data()
        print(f"📊 Created sample DataFrame with shape: {df.shape}")
        print(f"📋 Columns: {list(df.columns)}")
        
        # Test dashboard generation
        print("\n🎯 Testing improved inventory dashboard...")
        dashboard_result = generate_inventory_dashboard(df)
        
        if dashboard_result.get('success'):
            print("✅ Dashboard generation successful!")
            
            # Test summary items improvements
            summary_items = dashboard_result.get('summary_items', [])
            print(f"\n📋 Generated {len(summary_items)} summary items:")
            
            consolidated_cards_found = 0
            for item in summary_items:
                print(f"  - {item['label']}: {item['value']} ({item.get('icon', 'no-icon')})")
                
                # Check for consolidated percentage information
                if 'secondary_info' in item:
                    print(f"    └─ {item['secondary_info']}")
                    consolidated_cards_found += 1
                
                if 'trend' in item:
                    print(f"    └─ Trend: {item['trend']}")
            
            print(f"\n✅ Found {consolidated_cards_found} cards with consolidated percentage information")
            
            # Test charts improvements
            charts = dashboard_result.get('charts', [])
            print(f"\n📊 Generated {len(charts)} charts:")
            
            cash_flow_meter_found = False
            category_breakdown_found = False
            
            for chart in charts:
                print(f"  - {chart['title']} ({chart['type']})")
                
                # Check for new Cash Flow Efficiency Meter
                if 'cash_flow_efficiency_meter' in chart.get('id', ''):
                    cash_flow_meter_found = True
                    print(f"    ✅ Found Cash Flow Efficiency Meter!")
                    print(f"    └─ Subtitle: {chart.get('subtitle', 'N/A')}")
                
                # Check for category breakdown
                if 'category_cash_flow_breakdown' in chart.get('id', ''):
                    category_breakdown_found = True
                    print(f"    ✅ Found Category Cash Flow Breakdown!")
            
            # Validation summary
            print(f"\n🔍 Validation Results:")
            print(f"  ✅ Dashboard generation: SUCCESS")
            print(f"  ✅ Consolidated cards: {consolidated_cards_found > 0}")
            print(f"  ✅ Cash Flow Efficiency Meter: {cash_flow_meter_found}")
            print(f"  ✅ Category Cash Flow Breakdown: {category_breakdown_found}")
            
            if consolidated_cards_found > 0 and cash_flow_meter_found:
                print(f"\n🎉 All improvements successfully implemented!")
                return True
            else:
                print(f"\n⚠️ Some improvements may not be working as expected")
                return False
                
        else:
            print(f"❌ Dashboard generation failed: {dashboard_result.get('error')}")
            return False
            
    except ImportError as e:
        print(f"❌ Import error: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 Starting Dashboard Improvements Validation")
    print("=" * 50)
    
    success = test_dashboard_improvements()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 VALIDATION PASSED: All improvements are working correctly!")
    else:
        print("❌ VALIDATION FAILED: Some issues need to be addressed")
    
    print("🏁 Validation completed")
